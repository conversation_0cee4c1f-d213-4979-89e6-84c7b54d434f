{"extends": "./tsconfig.json", "compilerOptions": {"module": "commonjs", "target": "ES2020", "moduleResolution": "node", "esModuleInterop": true, "allowSyntheticDefaultImports": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "declaration": false, "outDir": "./dist", "rootDir": "./src"}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", ".next"], "ts-node": {"compilerOptions": {"module": "commonjs", "target": "ES2020", "moduleResolution": "node"}}}