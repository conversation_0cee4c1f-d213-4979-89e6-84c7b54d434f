# Production Environment Variables Template
# Copy this file to .env.production and fill in your actual values
# NEVER commit .env.production to version control!

# ==============================================
# APPLICATION CONFIGURATION
# ==============================================
NODE_ENV=production
NEXT_PUBLIC_API_URL=https://your-domain.com

# ==============================================
# DATABASE CONFIGURATION
# ==============================================
# Main application database URL
DATABASE_URL="**************************************/manga-next?schema=public&sslmode=require"

# ==============================================
# AUTHENTICATION (NextAuth.js)
# ==============================================
# Generate a secure secret: openssl rand -base64 32
NEXTAUTH_SECRET="your-super-secret-key-minimum-32-characters-long-replace-this"
NEXTAUTH_URL="https://your-domain.com"

# ==============================================
# REVALIDATION CONFIGURATION
# ==============================================
# Secret key for securing revalidation API endpoint, keep it safe! (or default)
REVALIDATION_SECRET="your-secret-key-here"

# ==============================================
# OPTIONAL: REDIS CONFIGURATION
# ==============================================
REDIS_URL="redis://username:password@redis:6379"
REDIS_PASSWORD="your-redis-password"

# ==============================================
# OPTIONAL: EMAIL CONFIGURATION
# ==============================================
# SMTP_HOST="smtp.gmail.com"
# SMTP_PORT="587"
# SMTP_USER="<EMAIL>"
# SMTP_PASS="your-app-password"

# ==============================================
# OPTIONAL: EXTERNAL API KEYS
# ==============================================
# MANGARAW_API_TOKEN="your-api-token-if-needed"

# ==============================================
# OPTIONAL: FILE STORAGE (AWS S3)
# ==============================================
# AWS_ACCESS_KEY_ID="your-aws-access-key"
# AWS_SECRET_ACCESS_KEY="your-aws-secret-key"
# AWS_REGION="us-east-1"
# AWS_S3_BUCKET="your-bucket-name"

# ==============================================
# OPTIONAL: MONITORING AND ANALYTICS
# ==============================================
# SENTRY_DSN="your-sentry-dsn"
# GOOGLE_ANALYTICS_ID="your-ga-id"

# ==============================================
# OPTIONAL: APPLICATION FEATURES
# ==============================================
# Enable database seeding on startup (usually false in production)
SEED_DATABASE=false

# ==============================================
# SECURITY HEADERS
# ==============================================
# Content Security Policy
# CSP_HEADER="default-src 'self'; script-src 'self' 'unsafe-eval'; style-src 'self' 'unsafe-inline';"
