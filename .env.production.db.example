# Database Environment Variables Template
# Copy this file to .env.production.db and fill in your actual values
# NEVER commit .env.production.db to version control!

# ==============================================
# POSTGRESQL CONFIGURATION
# ==============================================
POSTGRES_DB=manga-next
POSTGRES_USER=postgres
POSTGRES_PASSWORD=your-secure-database-password-replace-this

# PostgreSQL initialization arguments
POSTGRES_INITDB_ARGS=--encoding=UTF-8 --lc-collate=C --lc-ctype=C

# ==============================================
# SECURITY NOTES
# ==============================================
# 1. Use a strong password (minimum 16 characters)
# 2. Include uppercase, lowercase, numbers, and symbols
# 3. Don't use common words or patterns
# 4. Consider using a password manager to generate secure passwords
#
# Example strong password generation:
# openssl rand -base64 32
