const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function checkRankingData() {
  console.log('🔍 Checking ranking data in database...\n');

  try {
    // Check Comics table view statistics
    console.log('📊 Comics View Statistics:');
    console.log('========================');
    
    const comics = await prisma.comics.findMany({
      select: {
        id: true,
        title: true,
        slug: true,
        daily_views: true,
        weekly_views: true,
        monthly_views: true,
        total_views: true,
        total_favorites: true,
      },
      take: 10,
      orderBy: { total_views: 'desc' }
    });

    if (comics.length === 0) {
      console.log('❌ No comics found in database');
      return;
    }

    comics.forEach((comic, index) => {
      console.log(`${index + 1}. ${comic.title}`);
      console.log(`   - Daily: ${comic.daily_views || 0}`);
      console.log(`   - Weekly: ${comic.weekly_views || 0}`);
      console.log(`   - Monthly: ${comic.monthly_views || 0}`);
      console.log(`   - Total: ${comic.total_views || 0}`);
      console.log(`   - Favorites: ${comic.total_favorites || 0}`);
      console.log('');
    });

    // Check if any comics have non-zero view statistics
    const comicsWithViews = await prisma.comics.findMany({
      where: {
        OR: [
          { daily_views: { gt: 0 } },
          { weekly_views: { gt: 0 } },
          { monthly_views: { gt: 0 } }
        ]
      },
      select: {
        id: true,
        title: true,
        daily_views: true,
        weekly_views: true,
        monthly_views: true,
      }
    });

    console.log(`📈 Comics with non-zero view statistics: ${comicsWithViews.length}`);
    
    if (comicsWithViews.length === 0) {
      console.log('⚠️  All comics have 0 daily/weekly/monthly views');
      console.log('💡 This is why rankings API returns empty results');
      
      // Let's update some test data
      console.log('\n🔧 Updating test data...');
      
      const testComics = comics.slice(0, 5);
      for (let i = 0; i < testComics.length; i++) {
        const comic = testComics[i];
        await prisma.comics.update({
          where: { id: comic.id },
          data: {
            daily_views: Math.floor(Math.random() * 100) + 50,
            weekly_views: Math.floor(Math.random() * 500) + 200,
            monthly_views: Math.floor(Math.random() * 2000) + 1000,
          }
        });
        console.log(`✅ Updated ${comic.title} with test view data`);
      }
      
      console.log('\n🎉 Test data updated! Rankings should now work.');
    } else {
      console.log('✅ Found comics with view statistics:');
      comicsWithViews.forEach(comic => {
        console.log(`   - ${comic.title}: ${comic.daily_views}d/${comic.weekly_views}w/${comic.monthly_views}m`);
      });
    }

    // Test the ranking query directly
    console.log('\n🧪 Testing ranking query...');
    const rankingTest = await prisma.comics.findMany({
      where: {
        weekly_views: { gt: 0 }
      },
      select: {
        id: true,
        title: true,
        weekly_views: true,
      },
      orderBy: [
        { weekly_views: 'desc' },
        { total_views: 'desc' },
        { id: 'asc' }
      ],
      take: 5
    });

    console.log(`Found ${rankingTest.length} comics for weekly rankings:`);
    rankingTest.forEach((comic, index) => {
      console.log(`   ${index + 1}. ${comic.title} - ${comic.weekly_views} weekly views`);
    });

  } catch (error) {
    console.error('❌ Error checking ranking data:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkRankingData();
