@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

:root {
  --background: rgb(245, 245, 255);
  --foreground: rgb(42, 42, 74);
  --card: rgb(255, 255, 255);
  --card-foreground: rgb(42, 42, 74);
  --popover: rgb(255, 255, 255);
  --popover-foreground: rgb(42, 42, 74);
  --primary: rgb(110, 86, 207);
  --primary-foreground: rgb(255, 255, 255);
  --secondary: rgb(228, 223, 255);
  --secondary-foreground: rgb(74, 64, 128);
  --muted: rgb(240, 240, 250);
  --muted-foreground: rgb(108, 108, 138);
  --accent: rgb(216, 230, 255);
  --accent-foreground: rgb(42, 42, 74);
  --destructive: rgb(255, 84, 112);
  --destructive-foreground: rgb(255, 255, 255);
  --border: rgb(224, 224, 240);
  --input: rgb(224, 224, 240);
  --ring: rgb(110, 86, 207);
  --chart-1: rgb(110, 86, 207);
  --chart-2: rgb(158, 140, 252);
  --chart-3: rgb(93, 95, 239);
  --chart-4: rgb(124, 117, 250);
  --chart-5: rgb(71, 64, 179);
  --sidebar: rgb(240, 240, 250);
  --sidebar-foreground: rgb(42, 42, 74);
  --sidebar-primary: rgb(110, 86, 207);
  --sidebar-primary-foreground: rgb(255, 255, 255);
  --sidebar-accent: rgb(216, 230, 255);
  --sidebar-accent-foreground: rgb(42, 42, 74);
  --sidebar-border: rgb(224, 224, 240);
  --sidebar-ring: rgb(110, 86, 207);
  --font-sans: var(--font-nunito), sans-serif;
  --font-serif: Georgia, serif;
  --font-mono: JetBrains Mono, monospace;
  --radius: 0.5rem;
  --shadow-2xs: 0px 4px 10px 0px hsl(240 30% 25% / 0.06);
  --shadow-xs: 0px 4px 10px 0px hsl(240 30% 25% / 0.06);
  --shadow-sm: 0px 4px 10px 0px hsl(240 30% 25% / 0.12), 0px 1px 2px -1px hsl(240 30% 25% / 0.12);
  --shadow: 0px 4px 10px 0px hsl(240 30% 25% / 0.12), 0px 1px 2px -1px hsl(240 30% 25% / 0.12);
  --shadow-md: 0px 4px 10px 0px hsl(240 30% 25% / 0.12), 0px 2px 4px -1px hsl(240 30% 25% / 0.12);
  --shadow-lg: 0px 4px 10px 0px hsl(240 30% 25% / 0.12), 0px 4px 6px -1px hsl(240 30% 25% / 0.12);
  --shadow-xl: 0px 4px 10px 0px hsl(240 30% 25% / 0.12), 0px 8px 10px -1px hsl(240 30% 25% / 0.12);
  --shadow-2xl: 0px 4px 10px 0px hsl(240 30% 25% / 0.30);
}

.dark {
  --background: rgb(15, 15, 26);
  --foreground: rgb(226, 226, 245);
  --card: rgb(26, 26, 46);
  --card-foreground: rgb(226, 226, 245);
  --popover: rgb(26, 26, 46);
  --popover-foreground: rgb(226, 226, 245);
  --primary: rgb(164, 143, 255);
  --primary-foreground: rgb(15, 15, 26);
  --secondary: rgb(45, 43, 85);
  --secondary-foreground: rgb(196, 194, 255);
  --muted: rgb(34, 34, 68);
  --muted-foreground: rgb(160, 160, 192);
  --accent: rgb(48, 48, 96);
  --accent-foreground: rgb(226, 226, 245);
  --destructive: rgb(255, 84, 112);
  --destructive-foreground: rgb(255, 255, 255);
  --border: rgb(48, 48, 82);
  --input: rgb(48, 48, 82);
  --ring: rgb(164, 143, 255);
  --chart-1: rgb(164, 143, 255);
  --chart-2: rgb(121, 134, 203);
  --chart-3: rgb(100, 181, 246);
  --chart-4: rgb(77, 182, 172);
  --chart-5: rgb(255, 121, 198);
  --sidebar: rgb(26, 26, 46);
  --sidebar-foreground: rgb(226, 226, 245);
  --sidebar-primary: rgb(164, 143, 255);
  --sidebar-primary-foreground: rgb(15, 15, 26);
  --sidebar-accent: rgb(48, 48, 96);
  --sidebar-accent-foreground: rgb(226, 226, 245);
  --sidebar-border: rgb(48, 48, 82);
  --sidebar-ring: rgb(164, 143, 255);
  --font-sans: var(--font-nunito), sans-serif;
  --font-serif: Georgia, serif;
  --font-mono: JetBrains Mono, monospace;
  --radius: 0.5rem;
  --shadow-2xs: 0px 4px 10px 0px hsl(240 30% 25% / 0.06);
  --shadow-xs: 0px 4px 10px 0px hsl(240 30% 25% / 0.06);
  --shadow-sm: 0px 4px 10px 0px hsl(240 30% 25% / 0.12), 0px 1px 2px -1px hsl(240 30% 25% / 0.12);
  --shadow: 0px 4px 10px 0px hsl(240 30% 25% / 0.12), 0px 1px 2px -1px hsl(240 30% 25% / 0.12);
  --shadow-md: 0px 4px 10px 0px hsl(240 30% 25% / 0.12), 0px 2px 4px -1px hsl(240 30% 25% / 0.12);
  --shadow-lg: 0px 4px 10px 0px hsl(240 30% 25% / 0.12), 0px 4px 6px -1px hsl(240 30% 25% / 0.12);
  --shadow-xl: 0px 4px 10px 0px hsl(240 30% 25% / 0.12), 0px 8px 10px -1px hsl(240 30% 25% / 0.12);
  --shadow-2xl: 0px 4px 10px 0px hsl(240 30% 25% / 0.30);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);

  --font-sans: var(--font-nunito), sans-serif;
  --font-mono: var(--font-mono);
  --font-serif: var(--font-serif);

  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);

  --shadow-2xs: var(--shadow-2xs);
  --shadow-xs: var(--shadow-xs);
  --shadow-sm: var(--shadow-sm);
  --shadow: var(--shadow);
  --shadow-md: var(--shadow-md);
  --shadow-lg: var(--shadow-lg);
  --shadow-xl: var(--shadow-xl);
  --shadow-2xl: var(--shadow-2xl);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground font-sans;
  }

  /* Đánh dấu các chapter đã đọc bằng a:visited */
  .chapter-link:visited:not(.read-chapter) .font-medium {
    @apply text-primary/70;
  }

  .chapter-link:visited:not(.read-chapter) {
    @apply opacity-80;
  }

  /* Custom scrollbar styles */
  .scrollbar-thin {
    scrollbar-width: thin;
  }

  .scrollbar-thin::-webkit-scrollbar {
    width: 6px;
  }

  .scrollbar-thin::-webkit-scrollbar-track {
    background: transparent;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb {
    background-color: hsl(var(--border));
    border-radius: 3px;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background-color: hsl(var(--border) / 0.8);
  }

  /* Hidden scrollbar for title sections */
  .scrollbar-hidden {
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* Internet Explorer 10+ */
  }

  .scrollbar-hidden::-webkit-scrollbar {
    display: none; /* WebKit */
  }

  /* Full-Screen Loader Animations */
  @keyframes loading-dots {
    0%, 20% {
      opacity: 0.4;
      transform: scale(0.8);
    }
    50% {
      opacity: 1;
      transform: scale(1);
    }
    80%, 100% {
      opacity: 0.4;
      transform: scale(0.8);
    }
  }

  .loading-dot-1 {
    animation: loading-dots 1.5s infinite;
    animation-delay: 0s;
  }

  .loading-dot-2 {
    animation: loading-dots 1.5s infinite;
    animation-delay: 0.2s;
  }

  .loading-dot-3 {
    animation: loading-dots 1.5s infinite;
    animation-delay: 0.4s;
  }
}
