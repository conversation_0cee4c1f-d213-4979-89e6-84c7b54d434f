# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.*
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/versions

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# env files (can opt-in for committing if needed)
.env.local
.env.development.local
.env.test.local
.env.production.local
.env

# Production environment files (NEVER commit these!)
.env.production
.env.production.db

# Docker secrets
secrets/

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts
public/apple-touch-icon*.png
public/icon*.png
public/favicon*.png

# PM2 logs and runtime files
logs/
.pm2/
pm2.log
ecosystem.config.js.backup
