generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Authors {
  id            Int             @id @default(autoincrement())
  name          String          @db.Var<PERSON>har(255)
  slug          String          @unique @db.VarChar(255)
  bio           String?
  avatar_url    String?         @db.VarChar(255)
  created_at    DateTime?       @default(now()) @db.Timestamptz(6)
  updated_at    DateTime?       @default(now()) @db.Timestamptz(6)
  Comic_Authors Comic_Authors[]
}

model Chapter_Views {
  id         BigInt    @id @default(autoincrement())
  chapter_id Int
  user_id    Int?
  ip_address String?   @db.VarChar(45)
  user_agent String?
  viewed_at  DateTime? @default(now()) @db.Timestamptz(6)
  Chapters   Chapters  @relation(fields: [chapter_id], references: [id], onDelete: Cascade, onUpdate: NoAction)
  Users      Users?    @relation(fields: [user_id], references: [id], onUpdate: NoAction)

  @@index([chapter_id, viewed_at(sort: Desc)], map: "idx_chapter_views_chapter_id_viewed_at")
  @@index([viewed_at(sort: Desc)], map: "idx_chapter_views_viewed_at")
}

model Chapters {
  id               Int                @id @default(autoincrement())
  comic_id         Int
  title            String?            @db.VarChar(255)
  slug             String             @db.VarChar(255)
  release_date     DateTime?          @default(now()) @db.Timestamptz(6)
  view_count       Int?               @default(0)
  uploader_id      Int?
  created_at       DateTime?          @default(now()) @db.Timestamptz(6)
  updated_at       DateTime?          @default(now()) @db.Timestamptz(6)
  chapter_number   Float
  daily_views      Int?               @default(0)
  monthly_views    Int?               @default(0)
  weekly_views     Int?               @default(0)
  ChapterReports   ChapterReport[]
  Chapter_Views    Chapter_Views[]
  Comics           Comics             @relation(fields: [comic_id], references: [id], onDelete: Cascade, onUpdate: NoAction)
  Users            Users?             @relation(fields: [uploader_id], references: [id], onUpdate: NoAction)
  Comments         Comments[]
  Pages            Pages[]
  Reading_Progress Reading_Progress[]

  @@unique([comic_id, chapter_number])
  @@unique([comic_id, slug])
  @@index([comic_id, release_date(sort: Desc)], map: "idx_chapters_comic_id_release_date")
}

model Comic_Authors {
  comic_id   Int
  author_id  Int
  role       String    @db.VarChar(100)
  created_at DateTime? @default(now()) @db.Timestamptz(6)
  Authors    Authors   @relation(fields: [author_id], references: [id], onDelete: Cascade, onUpdate: NoAction)
  Comics     Comics    @relation(fields: [comic_id], references: [id], onDelete: Cascade, onUpdate: NoAction)

  @@id([comic_id, author_id, role])
}

model Comic_Genres {
  comic_id   Int
  genre_id   Int
  created_at DateTime? @default(now()) @db.Timestamptz(6)
  Comics     Comics    @relation(fields: [comic_id], references: [id], onDelete: Cascade, onUpdate: NoAction)
  Genres     Genres    @relation(fields: [genre_id], references: [id], onDelete: Cascade, onUpdate: NoAction)

  @@id([comic_id, genre_id])
  @@index([genre_id], map: "idx_comic_genres_genre_id")
  @@index([comic_id], map: "idx_comic_genres_comic_id")
}

model Comic_Publishers {
  comic_id     Int
  publisher_id Int
  created_at   DateTime?  @default(now()) @db.Timestamptz(6)
  Comics       Comics     @relation(fields: [comic_id], references: [id], onDelete: Cascade, onUpdate: NoAction)
  Publishers   Publishers @relation(fields: [publisher_id], references: [id], onDelete: Cascade, onUpdate: NoAction)

  @@id([comic_id, publisher_id])
}

model Comic_Views {
  id         BigInt    @id @default(autoincrement())
  comic_id   Int
  user_id    Int?
  ip_address String?   @db.VarChar(45)
  user_agent String?
  viewed_at  DateTime? @default(now()) @db.Timestamptz(6)
  Comics     Comics    @relation(fields: [comic_id], references: [id], onDelete: Cascade, onUpdate: NoAction)
  Users      Users?    @relation(fields: [user_id], references: [id], onUpdate: NoAction)

  @@index([comic_id, viewed_at(sort: Desc)], map: "idx_comic_views_comic_id_viewed_at")
  @@index([viewed_at(sort: Desc)], map: "idx_comic_views_viewed_at")
}

model View_Statistics {
  id            BigInt    @id @default(autoincrement())
  entity_type   String    @db.VarChar(20)
  entity_id     Int
  date          DateTime  @db.Date
  daily_views   Int       @default(0)
  weekly_views  Int       @default(0)
  monthly_views Int       @default(0)
  created_at    DateTime? @default(now()) @db.Timestamptz(6)
  updated_at    DateTime? @default(now()) @db.Timestamptz(6)

  @@unique([entity_type, entity_id, date])
  @@index([entity_type, entity_id, date(sort: Desc)], map: "idx_view_stats_entity_date")
  @@index([date(sort: Desc)], map: "idx_view_stats_date")
}

model Comics {
  id                       Int                      @id @default(autoincrement())
  title                    String                   @db.VarChar(255)
  slug                     String                   @unique @db.VarChar(255)
  alternative_titles       Json?
  description              String?
  cover_image_url          String?                  @db.VarChar(255)
  status                   String                   @default("ongoing") @db.VarChar(50)
  release_date             DateTime?                @db.Date
  country_of_origin        String?                  @db.VarChar(100)
  age_rating               String?                  @db.VarChar(50)
  total_views              Int?                     @default(0)
  total_favorites          Int?                     @default(0)
  last_chapter_uploaded_at DateTime?                @db.Timestamptz(6)
  uploader_id              Int?
  created_at               DateTime?                @default(now()) @db.Timestamptz(6)
  updated_at               DateTime?                @default(now()) @db.Timestamptz(6)
  search_vector            Unsupported("tsvector")?
  daily_views              Int?                     @default(0)
  monthly_views            Int?                     @default(0)
  weekly_views             Int?                     @default(0)
  Chapters                 Chapters[]
  Comic_Authors            Comic_Authors[]
  Comic_Genres             Comic_Genres[]
  Comic_Publishers         Comic_Publishers[]
  Comic_Views              Comic_Views[]
  Users                    Users?                   @relation(fields: [uploader_id], references: [id], onUpdate: NoAction)
  Comments                 Comments[]
  Favorites                Favorites[]
  Ratings                  Ratings[]
  Reading_Progress         Reading_Progress[]

  @@index([last_chapter_uploaded_at(sort: Desc)], map: "idx_comics_last_chapter_uploaded_at")
  @@index([status], map: "idx_comics_status")
  @@index([title], map: "idx_comics_title")
  @@index([search_vector], map: "idx_comics_search_vector", type: Gin)
  @@index([total_views(sort: Desc)], map: "idx_comics_total_views_desc")
  @@index([total_favorites(sort: Desc)], map: "idx_comics_total_favorites_desc")
  @@index([status, last_chapter_uploaded_at(sort: Desc)], map: "idx_comics_status_last_chapter")
  @@index([status, total_views(sort: Desc)], map: "idx_comics_status_total_views")
  @@index([created_at(sort: Desc)], map: "idx_comics_created_at_desc")
}

/// Enhanced Comments model with moderation and engagement features
model Comments {
  id                Int             @id @default(autoincrement())
  user_id           Int
  comic_id          Int?
  chapter_id        Int?
  parent_comment_id Int?
  content           String
  created_at        DateTime?       @default(now()) @db.Timestamptz(6)
  updated_at        DateTime?       @default(now()) @db.Timestamptz(6)
  dislikes_count    Int             @default(0)
  likes_count       Int             @default(0)
  edit_count        Int             @default(0)
  status            CommentStatus   @default(APPROVED)
  CommentLikes      CommentLike[]
  CommentReports    CommentReport[]
  Chapters          Chapters?       @relation(fields: [chapter_id], references: [id], onDelete: Cascade, onUpdate: NoAction)
  Comics            Comics?         @relation(fields: [comic_id], references: [id], onDelete: Cascade, onUpdate: NoAction)
  Comments          Comments?       @relation("CommentsToComments", fields: [parent_comment_id], references: [id], onDelete: Cascade, onUpdate: NoAction)
  other_Comments    Comments[]      @relation("CommentsToComments")
  Users             Users           @relation(fields: [user_id], references: [id], onDelete: Cascade, onUpdate: NoAction)

  @@index([chapter_id, created_at(sort: Desc)], map: "idx_comments_chapter_id_created_at")
  @@index([comic_id, created_at(sort: Desc)], map: "idx_comments_comic_id_created_at")
  @@index([user_id], map: "idx_comments_user_id")
  @@index([status], map: "idx_comments_status")
  @@index([parent_comment_id], map: "idx_comments_parent_id")
  @@index([comic_id, status, created_at(sort: Desc)], map: "idx_comments_comic_status_created")
  @@index([chapter_id, status, created_at(sort: Desc)], map: "idx_comments_chapter_status_created")
  @@index([likes_count(sort: Desc), created_at(sort: Desc)], map: "idx_comments_likes_created")
  @@index([status, created_at(sort: Desc)], map: "idx_comments_status_created_at")
}

/// Comment likes and dislikes tracking
model CommentLike {
  id         Int      @id @default(autoincrement())
  user_id    Int
  comment_id Int
  is_like    Boolean
  created_at DateTime @default(now()) @db.Timestamptz(6)
  Comments   Comments @relation(fields: [comment_id], references: [id], onDelete: Cascade)
  Users      Users    @relation(fields: [user_id], references: [id], onDelete: Cascade)

  @@unique([user_id, comment_id])
  @@index([comment_id], map: "idx_comment_likes_comment_id")
}

/// Comment reporting for moderation
model CommentReport {
  id         Int      @id @default(autoincrement())
  user_id    Int
  comment_id Int
  reason     String   @db.VarChar(255)
  details    String?
  created_at DateTime @default(now()) @db.Timestamptz(6)
  Comments   Comments @relation(fields: [comment_id], references: [id], onDelete: Cascade)
  Users      Users    @relation(fields: [user_id], references: [id], onDelete: Cascade)

  @@unique([user_id, comment_id])
  @@index([comment_id], map: "idx_comment_reports_comment_id")
}

/// Chapter error reporting for content issues
model ChapterReport {
  id         Int      @id @default(autoincrement())
  user_id    Int
  chapter_id Int
  reason     String   @db.VarChar(255)
  details    String?
  status     String   @default("pending") @db.VarChar(50)
  created_at DateTime @default(now()) @db.Timestamptz(6)
  updated_at DateTime @default(now()) @db.Timestamptz(6)
  Chapters   Chapters @relation(fields: [chapter_id], references: [id], onDelete: Cascade)
  Users      Users    @relation(fields: [user_id], references: [id], onDelete: Cascade)

  @@unique([user_id, chapter_id])
  @@index([chapter_id], map: "idx_chapter_reports_chapter_id")
  @@index([status], map: "idx_chapter_reports_status")
}

model Favorites {
  user_id    Int
  comic_id   Int
  created_at DateTime? @default(now()) @db.Timestamptz(6)
  Comics     Comics    @relation(fields: [comic_id], references: [id], onDelete: Cascade, onUpdate: NoAction)
  Users      Users     @relation(fields: [user_id], references: [id], onDelete: Cascade, onUpdate: NoAction)

  @@id([user_id, comic_id])
  @@index([user_id, created_at(sort: Desc)], map: "idx_favorites_user_id_created_at")
  @@index([comic_id, created_at(sort: Desc)], map: "idx_favorites_comic_id_created_at")
}

model Genres {
  id           Int            @id @default(autoincrement())
  name         String         @unique @db.VarChar(255)
  slug         String         @unique @db.VarChar(255)
  description  String?
  created_at   DateTime?      @default(now()) @db.Timestamptz(6)
  updated_at   DateTime?      @default(now()) @db.Timestamptz(6)
  Comic_Genres Comic_Genres[]
}

model Pages {
  id             Int       @id @default(autoincrement())
  chapter_id     Int
  page_number    Int
  image_url      String    @db.VarChar(255)
  image_alt_text String?   @db.VarChar(255)
  created_at     DateTime? @default(now()) @db.Timestamptz(6)
  Chapters       Chapters  @relation(fields: [chapter_id], references: [id], onDelete: Cascade, onUpdate: NoAction)

  @@unique([chapter_id, page_number])
  @@index([chapter_id, page_number], map: "idx_pages_chapter_id_page_number")
}

model Publishers {
  id               Int                @id @default(autoincrement())
  name             String             @unique @db.VarChar(255)
  slug             String             @unique @db.VarChar(255)
  description      String?
  logo_url         String?            @db.VarChar(255)
  created_at       DateTime?          @default(now()) @db.Timestamptz(6)
  updated_at       DateTime?          @default(now()) @db.Timestamptz(6)
  Comic_Publishers Comic_Publishers[]
}

/// This table contains check constraints and requires additional setup for migrations. Visit https://pris.ly/d/check-constraints for more info.
model Reading_Progress {
  user_id               Int
  comic_id              Int
  last_read_chapter_id  Int?
  last_read_page_number Int?
  progress_percentage   Float?    @db.Real
  updated_at            DateTime? @default(now()) @db.Timestamptz(6)
  Comics                Comics    @relation(fields: [comic_id], references: [id], onDelete: Cascade, onUpdate: NoAction)
  Chapters              Chapters? @relation(fields: [last_read_chapter_id], references: [id], onUpdate: NoAction)
  Users                 Users     @relation(fields: [user_id], references: [id], onDelete: Cascade, onUpdate: NoAction)

  @@id([user_id, comic_id])
  @@index([user_id, updated_at(sort: Desc)], map: "idx_reading_progress_user_updated")
  @@index([comic_id, updated_at(sort: Desc)], map: "idx_reading_progress_comic_updated")
}

/// Manga ratings model for star rating system
model Ratings {
  id         Int      @id @default(autoincrement())
  user_id    Int
  comic_id   Int
  rating     Float    @db.Real
  created_at DateTime @default(now()) @db.Timestamptz(6)
  updated_at DateTime @default(now()) @db.Timestamptz(6)
  Comics     Comics   @relation(fields: [comic_id], references: [id], onDelete: Cascade)
  Users      Users    @relation(fields: [user_id], references: [id], onDelete: Cascade)

  @@unique([user_id, comic_id])
  @@index([comic_id], map: "idx_ratings_comic_id")
  @@index([user_id], map: "idx_ratings_user_id")
  @@index([rating], map: "idx_ratings_rating")
}

model Users {
  id                   Int                   @id @default(autoincrement())
  username             String                @unique @db.VarChar(255)
  email                String                @unique @db.VarChar(255)
  password_hash        String                @db.VarChar(255)
  avatar_url           String?               @db.VarChar(255)
  role                 String                @default("user") @db.VarChar(50)
  created_at           DateTime?             @default(now()) @db.Timestamptz(6)
  updated_at           DateTime?             @default(now()) @db.Timestamptz(6)
  accounts             Account[]
  ChapterReports       ChapterReport[]
  Chapter_Views        Chapter_Views[]
  Chapters             Chapters[]
  Comic_Views          Comic_Views[]
  Comics               Comics[]
  CommentLikes         CommentLike[]
  CommentReports       CommentReport[]
  Comments             Comments[]
  Favorites            Favorites[]
  NotificationSettings NotificationSettings?
  Ratings              Ratings[]
  Reading_Progress     Reading_Progress[]
  sessions             Session[]
  UserNotifications    UserNotification[]
}

model Account {
  id                String  @id @default(cuid())
  userId            Int
  type              String
  provider          String
  providerAccountId String
  refresh_token     String?
  access_token      String?
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String?
  session_state     String?
  user              Users   @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       Int
  expires      DateTime
  user         Users    @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
}

/// User notification preferences and settings
model NotificationSettings {
  id                   Int      @id @default(autoincrement())
  user_id              Int      @unique
  in_app_notifications Boolean  @default(true)
  new_chapter_alerts   Boolean  @default(true)
  created_at           DateTime @default(now()) @db.Timestamptz(6)
  updated_at           DateTime @default(now()) @db.Timestamptz(6)
  Users                Users    @relation(fields: [user_id], references: [id], onDelete: Cascade)

  @@index([user_id], map: "idx_notification_settings_user_id")
}

/// Individual user notifications with read status
model UserNotification {
  id         Int       @id @default(autoincrement())
  user_id    Int
  type       String    @db.VarChar(50)
  title      String    @db.VarChar(255)
  message    String
  data       Json?
  is_read    Boolean   @default(false)
  created_at DateTime  @default(now()) @db.Timestamptz(6)
  read_at    DateTime? @db.Timestamptz(6)
  Users      Users     @relation(fields: [user_id], references: [id], onDelete: Cascade)

  @@index([user_id, is_read], map: "idx_user_notifications_user_read")
  @@index([user_id, created_at(sort: Desc)], map: "idx_user_notifications_user_created")
  @@index([type], map: "idx_user_notifications_type")
}

enum CommentStatus {
  PENDING
  APPROVED
  REJECTED
  FLAGGED
}
