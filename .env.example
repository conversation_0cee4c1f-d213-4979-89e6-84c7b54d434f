DATABASE_URL="postgresql://username:password:@localhost:5432/database-name"
NEXT_PUBLIC_API_URL="http://localhost:3000"

# MangaRaw API Configuration
MANGARAW_BASE_URL="https://manga.com/api"
MANGARAW_API_TOKEN="your-mangaraw-api-token"

NEXTAUTH_SECRET="your-super-secret-key-for-nextauth-jwt-encryption"
NEXTAUTH_URL="http://localhost:3000"

# On-demand Revalidation (Optional)
# Secret key for securing revalidation API endpoint
REVALIDATION_SECRET="your-secret-key-here"

# ==============================================
# SEO CONFIGURATION (Optional)
# ==============================================
# These variables override the default SEO config
# Leave empty to use defaults from seo.config.ts

# Basic site information
NEXT_PUBLIC_SITE_URL="http://localhost:3000"
NEXT_PUBLIC_SITE_NAME="Manga Next"
NEXT_PUBLIC_SITE_DESCRIPTION="Read Manga Online for Free"
NEXT_PUBLIC_SITE_TAGLINE="Read Manga Online for Free"
NEXT_PUBLIC_SITE_KEYWORDS="manga,comic,read manga,free manga,online manga"
NEXT_PUBLIC_SITE_LANGUAGE="en"
NEXT_PUBLIC_SITE_LOCALE="en_US"

# Assets configuration
NEXT_PUBLIC_LOGO_URL="/logo.png"
NEXT_PUBLIC_FAVICON_URL="/favicon.ico"
NEXT_PUBLIC_OG_IMAGE="/images/og-image.jpg"

# Social media configuration
NEXT_PUBLIC_TWITTER_HANDLE="@MangaNext"
NEXT_PUBLIC_TWITTER_CREATOR="@MangaNext"
NEXT_PUBLIC_TWITTER_CARD="summary_large_image"
NEXT_PUBLIC_FACEBOOK_PAGE="MangaNext"
NEXT_PUBLIC_OG_TYPE="website"

# SEO templates configuration
NEXT_PUBLIC_TITLE_TEMPLATE="%s | Your Site Name"
NEXT_PUBLIC_DEFAULT_TITLE="Your Site Name - Description"
NEXT_PUBLIC_TITLE_SEPARATOR=" | "

# Robots configuration
NEXT_PUBLIC_ROBOTS_INDEX="true"
NEXT_PUBLIC_ROBOTS_FOLLOW="true"

# Organization schema configuration
NEXT_PUBLIC_ORG_NAME="Your Organization"
NEXT_PUBLIC_ORG_LEGAL_NAME="Your Legal Organization Name"
NEXT_PUBLIC_ORG_FOUNDING_DATE="2024"
NEXT_PUBLIC_ORG_COUNTRY="US"

# Analytics configuration (for production)
# NEXT_PUBLIC_GOOGLE_ANALYTICS_ID="G-XXXXXXXXXX"
# NEXT_PUBLIC_GOOGLE_TAG_MANAGER_ID="GTM-XXXXXXX"
# NEXT_PUBLIC_GOOGLE_SITE_VERIFICATION="your-verification-code"
# NEXT_PUBLIC_FACEBOOK_PIXEL_ID="your-pixel-id"

# ==============================================
# PAGE TITLES CONFIGURATION (Optional)
# ==============================================
# Override specific page titles for different filters
# Format: NEXT_PUBLIC_PAGE_TITLE_{SECTION}_{TYPE}_{FILTER}
# Supports multiple languages based on NEXT_PUBLIC_SITE_LOCALE

# Examples for Vietnamese:
# NEXT_PUBLIC_PAGE_TITLE_MANGA_DEFAULT="Truyện Tranh Mới Nhất"
# NEXT_PUBLIC_PAGE_TITLE_MANGA_SORT_POPULAR="Truyện Tranh Phổ Biến"
# NEXT_PUBLIC_PAGE_TITLE_MANGA_SORT_RATING="Truyện Tranh Đánh Giá Cao"
# NEXT_PUBLIC_PAGE_TITLE_MANGA_STATUS_COMPLETED="Truyện Tranh Hoàn Thành"
# NEXT_PUBLIC_PAGE_TITLE_MANGA_STATUS_ONGOING="Truyện Tranh Đang Tiến Hành"

# Examples for English:
# NEXT_PUBLIC_PAGE_TITLE_MANGA_SORT_TRENDING="Trending Manga"
# NEXT_PUBLIC_PAGE_TITLE_MANGA_STATUS_HIATUS="Manga on Hiatus"
# NEXT_PUBLIC_PAGE_TITLE_MANGA_COMBINED_POPULAR_COMPLETED="Popular Completed Manga"