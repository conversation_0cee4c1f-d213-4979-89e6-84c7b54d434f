# Dependencies
node_modules
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# Testing
coverage
*.lcov

# Next.js
.next/
out/
build

# Production
dist

# Misc
.DS_Store
*.pem

# Debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Local env files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Vercel
.vercel

# TypeScript
*.tsbuildinfo
next-env.d.ts

# IDE
.vscode
.idea
*.swp
*.swo

# OS
Thumbs.db

# Git
.git
.gitignore
README.md

# Documentation
docs/
*.md

# Scripts (except docker-entrypoint.sh)
scripts/
!docker-entrypoint.sh

# Logs
logs
*.log

# Database
*.sql
local_dump.sql

# Temporary files
tmp/
temp/
